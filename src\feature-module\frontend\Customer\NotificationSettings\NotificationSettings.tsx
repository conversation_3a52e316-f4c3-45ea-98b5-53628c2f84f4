import React, { useEffect, useState } from 'react';

type Notification = {
  id: string;
  title: string;
  message: string;
  time: string;
  read: boolean;
};

const sampleNotifications: Notification[] = [
  {
    id: '1',
    title: 'Booking confirmed',
    message: 'Your booking for Plumbing on Sep 10 has been confirmed.',
    time: '2h ago',
    read: false,
  },
  {
    id: '2',
    title: 'New review received',
    message: 'You received a 5-star review from <PERSON>',
    time: '1d ago',
    read: true,
  },
  {
    id: '3',
    title: 'Payment successful',
    message: 'Your payment of $45.00 was processed.',
    time: '3d ago',
    read: true,
  },
];

const NotificationSettings: React.FC = () => {
  const [notifications, setNotifications] = useState<Notification[]>([]);
  const [emailEnabled, setEmailEnabled] = useState<boolean>(() => {
    try {
      const raw = localStorage.getItem('notif_email_enabled');
      return raw ? JSON.parse(raw) : true;
    } catch {
      return true;
    }
  });
  const [pushEnabled, setPushEnabled] = useState<boolean>(() => {
    try {
      const raw = localStorage.getItem('notif_push_enabled');
      return raw ? JSON.parse(raw) : true;
    } catch {
      return true;
    }
  });

  useEffect(() => {
    // load sample notifications (replace with real API call when available)
    setNotifications(sampleNotifications);
  }, []);

  useEffect(() => {
    localStorage.setItem('notif_email_enabled', JSON.stringify(emailEnabled));
  }, [emailEnabled]);

  useEffect(() => {
    localStorage.setItem('notif_push_enabled', JSON.stringify(pushEnabled));
  }, [pushEnabled]);

  const toggleRead = (id: string) => {
    setNotifications((prev) =>
      prev.map((n) => (n.id === id ? { ...n, read: !n.read } : n))
    );
  };

  const markAllRead = () => {
    setNotifications((prev) => prev.map((n) => ({ ...n, read: true })));
  };

  const clearAll = () => setNotifications([]);

  return (
    <div className="max-w-3xl mx-auto p-4">
      <div className="bg-white shadow rounded-lg overflow-hidden">
        <div className="flex items-center justify-between px-6 py-4 border-b">
          <div>
            <h2 className="text-lg font-semibold">Notifications</h2>
            <p className="text-sm text-gray-500">Manage your notification preferences and view recent activity.</p>
          </div>

          <div className="flex items-center gap-3">
            <button
              onClick={markAllRead}
              className="text-sm px-3 py-1 bg-indigo-600 text-white rounded hover:bg-indigo-700"
            >
              Mark all read
            </button>
            <button
              onClick={clearAll}
              className="text-sm px-3 py-1 border rounded hover:bg-gray-50"
            >
              Clear
            </button>
          </div>
        </div>

        <div className="px-6 py-4 border-b">
          <div className="flex items-center gap-6">
            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                checked={emailEnabled}
                onChange={() => setEmailEnabled((v) => !v)}
                className="w-4 h-4"
              />
              <span>Email notifications</span>
            </label>

            <label className="flex items-center gap-2 text-sm">
              <input
                type="checkbox"
                checked={pushEnabled}
                onChange={() => setPushEnabled((v) => !v)}
                className="w-4 h-4"
              />
              <span>Push notifications</span>
            </label>
          </div>
        </div>

        <div className="px-6 py-4">
          {notifications.length === 0 ? (
            <div className="text-center py-12 text-gray-500">
              <p className="mb-2">No notifications</p>
              <p className="text-xs">You're all caught up.</p>
            </div>
          ) : (
            <ul className="space-y-3">
              {notifications.map((n) => (
                <li
                  key={n.id}
                  className={`flex items-center gap-4 p-3 rounded-md border ${n.read ? 'bg-gray-50' : 'bg-white border-indigo-100 shadow-sm'} `}
                >
                  <div className="flex-shrink-0">
                    <div
                      className={`w-10 h-10 rounded-full flex items-center justify-center text-white font-semibold ${n.read ? 'bg-gray-400' : 'bg-indigo-600'}`}
                    >
                      {n.title.charAt(0).toUpperCase()}
                    </div>
                  </div>

                  <div className="flex-1 min-w-0">
                    <div className="flex items-center justify-between">
                      <div className="truncate">
                        <p className="text-sm font-medium truncate">{n.title}</p>
                        <p className="text-xs text-gray-500">{n.time}</p>
                      </div>

                      <div className="flex items-center gap-2">
                        <button
                          onClick={() => toggleRead(n.id)}
                          className="text-xs px-2 py-1 border rounded whitespace-nowrap"
                        >
                          {n.read ? 'Mark unread' : 'Mark read'}
                        </button>
                      </div>
                    </div>

                    <p className="mt-1 text-sm text-gray-700 truncate">{n.message}</p>
                  </div>
                </li>
              ))}
            </ul>
          )}
        </div>
      </div>
    </div>
  );
};

export default NotificationSettings;
